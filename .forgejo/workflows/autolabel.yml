on:
  pull_request_target:
    types: [opened, edited, synchronize, closed, assigned, labeled, unlabeled]
  issues:
    types: [opened, edited, closed, assigned, labeled, unlabeled]
  issue_comment:
    types: [created]

jobs:
  pr_labeler:
    runs-on: utilities
    if: ${{ github.event.sender.login != 'ffmpeg-devel' }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Label by file-changes
        uses: https://github.com/actions/labeler@v5
        if: ${{ forge.event_name == 'pull_request_target' }}
        with:
          configuration-path: .forgejo/labeler/labeler.yml
          repo-token: ${{ secrets.AUTOLABELER_TOKEN }}
      - name: Label by title-match
        uses: https://github.com/actions/github-script@v7
        with:
          script: |
            const script = require('.forgejo/labeler/labeler.js')
            await script({github, context})
          github-token: ${{ secrets.AUTOLABELER_TOKEN }}
