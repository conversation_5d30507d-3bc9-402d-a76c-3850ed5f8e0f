on:
  push:
    branches:
      - master
  pull_request:

jobs:
  run_fate:
    strategy:
      fail-fast: false
      matrix:
        runner: [linux-amd64,linux-aarch64]
        bits: ['64']
        include:
          - runner: linux-amd64
            bits: '32'
    runs-on: ${{ matrix.runner }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Configure
        run: |
          ./configure --enable-gpl --enable-nonfree --enable-memory-poisoning --assert-level=2 \
              $([ "${{ matrix.bits }}" != "32" ] || echo --arch=x86_32 --extra-cflags=-m32 --extra-cxxflags=-m32 --extra-ldflags=-m32) \
              || CFGRES=$? && CFGRES=$?
          cat ffbuild/config.log
          exit $CFGRES
      - name: Build
        run: make -j$(nproc)
      - name: Restore Cached Fate-Suite
        id: cache
        uses: actions/cache/restore@v4
        with:
          path: fate-suite
          key: fate-suite
          restore-keys: |
            fate-suite-
      - name: Sync Fate-Suite
        id: fate
        run: |
          make fate-rsync SAMPLES=$PWD/fate-suite
          echo "hash=$(find fate-suite -type f | sort | sha256sum | cut -d' ' -f1)" >> $FORGEJO_OUTPUT
      - name: Cache Fate-Suite
        uses: actions/cache/save@v4
        if: ${{ format('fate-suite-{0}', steps.fate.outputs.hash) != steps.cache.outputs.cache-matched-key }}
        with:
          path: fate-suite
          key: fate-suite-${{ steps.fate.outputs.hash }}
      - name: Run Fate
        run: make fate SAMPLES=$PWD/fate-suite -j$(nproc)
  compile_only:
    strategy:
      fail-fast: false
      matrix:
        image: ["ghcr.io/btbn/ffmpeg-builds/win64-gpl:latest"]
    runs-on: linux-amd64
    container: ${{ matrix.image }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Configure
        run: |
          ./configure --pkg-config-flags="--static" $FFBUILD_TARGET_FLAGS $FF_CONFIGURE \
              --cc="$CC" --cxx="$CXX" --ar="$AR" --ranlib="$RANLIB" --nm="$NM" \
              --extra-cflags="$FF_CFLAGS" --extra-cxxflags="$FF_CXXFLAGS" \
              --extra-libs="$FF_LIBS" --extra-ldflags="$FF_LDFLAGS" --extra-ldexeflags="$FF_LDEXEFLAGS"
      - name: Build
        run: make -j$(nproc)
