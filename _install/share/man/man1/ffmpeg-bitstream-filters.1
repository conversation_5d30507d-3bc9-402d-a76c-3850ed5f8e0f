.\" -*- mode: troff; coding: utf-8 -*-
.\" Automatically generated by Pod::Man 5.01 (Pod::Simple 3.43)
.\"
.\" Standard preamble:
.\" ========================================================================
.de Sp \" Vertical space (when we can't use .PP)
.if t .sp .5v
.if n .sp
..
.de Vb \" Begin verbatim text
.ft CW
.nf
.ne \\$1
..
.de Ve \" End verbatim text
.ft R
.fi
..
.\" \*(C` and \*(C' are quotes in nroff, nothing in troff, for use with C<>.
.ie n \{\
.    ds C` ""
.    ds C' ""
'br\}
.el\{\
.    ds C`
.    ds C'
'br\}
.\"
.\" Escape single quotes in literal strings from groff's Unicode transform.
.ie \n(.g .ds Aq \(aq
.el       .ds Aq '
.\"
.\" If the F register is >0, we'll generate index entries on stderr for
.\" titles (.TH), headers (.SH), subsections (.SS), items (.Ip), and index
.\" entries marked with X<> in POD.  Of course, you'll have to process the
.\" output yourself in some meaningful fashion.
.\"
.\" Avoid warning from groff about undefined register 'F'.
.de IX
..
.nr rF 0
.if \n(.g .if rF .nr rF 1
.if (\n(rF:(\n(.g==0)) \{\
.    if \nF \{\
.        de IX
.        tm Index:\\$1\t\\n%\t"\\$2"
..
.        if !\nF==2 \{\
.            nr % 0
.            nr F 2
.        \}
.    \}
.\}
.rr rF
.\" ========================================================================
.\"
.IX Title "FFMPEG-BITSTREAM-FILTERS 1"
.TH FFMPEG-BITSTREAM-FILTERS 1 " " " " " "
.\" For nroff, turn off justification.  Always turn off hyphenation; it makes
.\" way too many mistakes in technical documents.
.if n .ad l
.nh
.SH NAME
ffmpeg\-bitstream\-filters \- FFmpeg bitstream filters
.SH DESCRIPTION
.IX Header "DESCRIPTION"
This document describes the bitstream filters provided by the
libavcodec library.
.PP
A bitstream filter operates on the encoded stream data, and performs
bitstream level modifications without performing decoding.
.SH "BITSTREAM FILTERS"
.IX Header "BITSTREAM FILTERS"
When you configure your FFmpeg build, all the supported bitstream
filters are enabled by default. You can list all available ones using
the configure option \f(CW\*(C`\-\-list\-bsfs\*(C'\fR.
.PP
You can disable all the bitstream filters using the configure option
\&\f(CW\*(C`\-\-disable\-bsfs\*(C'\fR, and selectively enable any bitstream filter using
the option \f(CW\*(C`\-\-enable\-bsf=BSF\*(C'\fR, or you can disable a particular
bitstream filter using the option \f(CW\*(C`\-\-disable\-bsf=BSF\*(C'\fR.
.PP
The option \f(CW\*(C`\-bsfs\*(C'\fR of the ff* tools will display the list of
all the supported bitstream filters included in your build.
.PP
The ff* tools have a \-bsf option applied per stream, taking a
comma-separated list of filters, whose parameters follow the filter
name after a '='.
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-c:v copy \-bsf:v filter1[=opt1=str1:opt2=str2][,filter2] OUTPUT
.Ve
.PP
Below is a description of the currently available bitstream filters,
with their parameters, if any.
.SS aac_adtstoasc
.IX Subsection "aac_adtstoasc"
Convert MPEG\-2/4 AAC ADTS to an MPEG\-4 Audio Specific Configuration
bitstream.
.PP
This filter creates an MPEG\-4 AudioSpecificConfig from an MPEG\-2/4
ADTS header and removes the ADTS header.
.PP
This filter is required for example when copying an AAC stream from a
raw ADTS AAC or an MPEG-TS container to MP4A\-LATM, to an FLV file, or
to MOV/MP4 files and related formats such as 3GP or M4A. Please note
that it is auto-inserted for MP4A\-LATM and MOV/MP4 and related formats.
.SS av1_metadata
.IX Subsection "av1_metadata"
Modify metadata embedded in an AV1 stream.
.IP \fBtd\fR 4
.IX Item "td"
Insert or remove temporal delimiter OBUs in all temporal units of the
stream.
.RS 4
.IP \fBinsert\fR 4
.IX Item "insert"
Insert a TD at the beginning of every TU which does not already have one.
.IP \fBremove\fR 4
.IX Item "remove"
Remove the TD from the beginning of every TU which has one.
.RE
.RS 4
.RE
.IP \fBcolor_primaries\fR 4
.IX Item "color_primaries"
.PD 0
.IP \fBtransfer_characteristics\fR 4
.IX Item "transfer_characteristics"
.IP \fBmatrix_coefficients\fR 4
.IX Item "matrix_coefficients"
.PD
Set the color description fields in the stream (see AV1 section 6.4.2).
.IP \fBcolor_range\fR 4
.IX Item "color_range"
Set the color range in the stream (see AV1 section 6.4.2; note that
this cannot be set for streams using BT.709 primaries, sRGB transfer
characteristic and identity (RGB) matrix coefficients).
.RS 4
.IP \fBtv\fR 4
.IX Item "tv"
Limited range.
.IP \fBpc\fR 4
.IX Item "pc"
Full range.
.RE
.RS 4
.RE
.IP \fBchroma_sample_position\fR 4
.IX Item "chroma_sample_position"
Set the chroma sample location in the stream (see AV1 section 6.4.2).
This can only be set for 4:2:0 streams.
.RS 4
.IP \fBvertical\fR 4
.IX Item "vertical"
Left position (matching the default in MPEG\-2 and H.264).
.IP \fBcolocated\fR 4
.IX Item "colocated"
Top-left position.
.RE
.RS 4
.RE
.IP \fBtick_rate\fR 4
.IX Item "tick_rate"
Set the tick rate (\fItime_scale / num_units_in_display_tick\fR) in
the timing info in the sequence header.
.IP \fBnum_ticks_per_picture\fR 4
.IX Item "num_ticks_per_picture"
Set the number of ticks in each picture, to indicate that the stream
has a fixed framerate.  Ignored if \fBtick_rate\fR is not also set.
.IP \fBdelete_padding\fR 4
.IX Item "delete_padding"
Deletes Padding OBUs.
.SS chomp
.IX Subsection "chomp"
Remove zero padding at the end of a packet.
.SS dca_core
.IX Subsection "dca_core"
Extract the core from a DCA/DTS stream, dropping extensions such as
DTS-HD.
.SS dovi_rpu
.IX Subsection "dovi_rpu"
Manipulate Dolby Vision metadata in a HEVC/AV1 bitstream, optionally enabling
metadata compression.
.IP \fBstrip\fR 4
.IX Item "strip"
If enabled, strip all Dolby Vision metadata (configuration record + RPU data
blocks) from the stream.
.IP \fBcompression\fR 4
.IX Item "compression"
Which compression level to enable.
.RS 4
.IP \fBnone\fR 4
.IX Item "none"
No metadata compression.
.IP \fBlimited\fR 4
.IX Item "limited"
Limited metadata compression scheme. Should be compatible with most devices.
This is the default.
.IP \fBextended\fR 4
.IX Item "extended"
Extended metadata compression. Devices are not required to support this. Note
that this level currently behaves the same as \fBlimited\fR in libavcodec.
.RE
.RS 4
.RE
.SS dump_extra
.IX Subsection "dump_extra"
Add extradata to the beginning of the filtered packets except when
said packets already exactly begin with the extradata that is intended
to be added.
.IP \fBfreq\fR 4
.IX Item "freq"
The additional argument specifies which packets should be filtered.
It accepts the values:
.RS 4
.IP \fBk\fR 4
.IX Item "k"
.PD 0
.IP \fBkeyframe\fR 4
.IX Item "keyframe"
.PD
add extradata to all key packets
.IP \fBe\fR 4
.IX Item "e"
.PD 0
.IP \fBall\fR 4
.IX Item "all"
.PD
add extradata to all packets
.RE
.RS 4
.RE
.PP
If not specified it is assumed \fBk\fR.
.PP
For example the following \fBffmpeg\fR command forces a global
header (thus disabling individual packet headers) in the H.264 packets
generated by the \f(CW\*(C`libx264\*(C'\fR encoder, but corrects them by adding
the header stored in extradata to the key packets:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-map 0 \-flags:v +global_header \-c:v libx264 \-bsf:v dump_extra out.ts
.Ve
.SS dv_error_marker
.IX Subsection "dv_error_marker"
Blocks in DV which are marked as damaged are replaced by blocks of the specified color.
.IP \fBcolor\fR 4
.IX Item "color"
The color to replace damaged blocks by
.IP \fBsta\fR 4
.IX Item "sta"
A 16 bit mask which specifies which of the 16 possible error status values are
to be replaced by colored blocks. 0xFFFE is the default which replaces all non 0
error status values.
.RS 4
.IP \fBok\fR 4
.IX Item "ok"
No error, no concealment
.IP \fBerr\fR 4
.IX Item "err"
Error, No concealment
.IP \fBres\fR 4
.IX Item "res"
Reserved
.IP \fBnotok\fR 4
.IX Item "notok"
Error or concealment
.IP \fBnotres\fR 4
.IX Item "notres"
Not reserved
.IP "\fBAa, Ba, Ca, Ab, Bb, Cb, A, B, C, a, b, erri, erru\fR" 4
.IX Item "Aa, Ba, Ca, Ab, Bb, Cb, A, B, C, a, b, erri, erru"
The specific error status code
.RE
.RS 4
.Sp
see page 44\-46 or section 5.5 of
<\fBhttp://web.archive.org/web/20060927044735/http://www.smpte.org/smpte_store/standards/pdf/s314m.pdf\fR>
.RE
.SS eac3_core
.IX Subsection "eac3_core"
Extract the core from a E\-AC\-3 stream, dropping extra channels.
.SS extract_extradata
.IX Subsection "extract_extradata"
Extract the in-band extradata.
.PP
Certain codecs allow the long-term headers (e.g. MPEG\-2 sequence headers,
or H.264/HEVC (VPS/)SPS/PPS) to be transmitted either "in-band" (i.e. as a part
of the bitstream containing the coded frames) or "out of band" (e.g. on the
container level). This latter form is called "extradata" in FFmpeg terminology.
.PP
This bitstream filter detects the in-band headers and makes them available as
extradata.
.IP \fBremove\fR 4
.IX Item "remove"
When this option is enabled, the long-term headers are removed from the
bitstream after extraction.
.SS filter_units
.IX Subsection "filter_units"
Remove units with types in or not in a given set from the stream.
.IP \fBpass_types\fR 4
.IX Item "pass_types"
List of unit types or ranges of unit types to pass through while removing
all others.  This is specified as a '|'\-separated list of unit type values
or ranges of values with '\-'.
.IP \fBremove_types\fR 4
.IX Item "remove_types"
Identical to \fBpass_types\fR, except the units in the given set
removed and all others passed through.
.PP
The types used by pass_types and remove_types correspond to NAL unit types
(nal_unit_type) in H.264, HEVC and H.266 (see Table 7\-1 in the H.264
and HEVC specifications or Table 5 in the H.266 specification), to
marker values for JPEG (without 0xFF prefix) and to start codes without
start code prefix (i.e. the byte following the 0x000001) for MPEG\-2.
For VP8 and VP9, every unit has type zero.
.PP
Extradata is unchanged by this transformation, but note that if the stream
contains inline parameter sets then the output may be unusable if they are
removed.
.PP
For example, to remove all non-VCL NAL units from an H.264 stream:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-c:v copy \-bsf:v \*(Aqfilter_units=pass_types=1\-5\*(Aq OUTPUT
.Ve
.PP
To remove all AUDs, SEI and filler from an H.265 stream:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-c:v copy \-bsf:v \*(Aqfilter_units=remove_types=35|38\-40\*(Aq OUTPUT
.Ve
.PP
To remove all user data from a MPEG\-2 stream, including Closed Captions:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-c:v copy \-bsf:v \*(Aqfilter_units=remove_types=178\*(Aq OUTPUT
.Ve
.PP
To remove all SEI from a H264 stream, including Closed Captions:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-c:v copy \-bsf:v \*(Aqfilter_units=remove_types=6\*(Aq OUTPUT
.Ve
.PP
To remove all prefix and suffix SEI from a HEVC stream, including Closed Captions and dynamic HDR:
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-c:v copy \-bsf:v \*(Aqfilter_units=remove_types=39|40\*(Aq OUTPUT
.Ve
.SS hapqa_extract
.IX Subsection "hapqa_extract"
Extract Rgb or Alpha part of an HAPQA file, without recompression, in order to create an HAPQ or an HAPAlphaOnly file.
.IP \fBtexture\fR 4
.IX Item "texture"
Specifies the texture to keep.
.RS 4
.IP \fBcolor\fR 4
.IX Item "color"
.PD 0
.IP \fBalpha\fR 4
.IX Item "alpha"
.RE
.RS 4
.RE
.PD
.PP
Convert HAPQA to HAPQ
.PP
.Vb 1
\&        ffmpeg \-i hapqa_inputfile.mov \-c copy \-bsf:v hapqa_extract=texture=color \-tag:v HapY \-metadata:s:v:0 encoder="HAPQ" hapq_file.mov
.Ve
.PP
Convert HAPQA to HAPAlphaOnly
.PP
.Vb 1
\&        ffmpeg \-i hapqa_inputfile.mov \-c copy \-bsf:v hapqa_extract=texture=alpha \-tag:v HapA \-metadata:s:v:0 encoder="HAPAlpha Only" hapalphaonly_file.mov
.Ve
.SS h264_metadata
.IX Subsection "h264_metadata"
Modify metadata embedded in an H.264 stream.
.IP \fBaud\fR 4
.IX Item "aud"
Insert or remove AUD NAL units in all access units of the stream.
.RS 4
.IP \fBpass\fR 4
.IX Item "pass"
.PD 0
.IP \fBinsert\fR 4
.IX Item "insert"
.IP \fBremove\fR 4
.IX Item "remove"
.RE
.RS 4
.PD
.Sp
Default is pass.
.RE
.IP \fBsample_aspect_ratio\fR 4
.IX Item "sample_aspect_ratio"
Set the sample aspect ratio of the stream in the VUI parameters.
See H.264 table E\-1.
.IP \fBoverscan_appropriate_flag\fR 4
.IX Item "overscan_appropriate_flag"
Set whether the stream is suitable for display using overscan
or not (see H.264 section E.2.1).
.IP \fBvideo_format\fR 4
.IX Item "video_format"
.PD 0
.IP \fBvideo_full_range_flag\fR 4
.IX Item "video_full_range_flag"
.PD
Set the video format in the stream (see H.264 section E.2.1 and
table E\-2).
.IP \fBcolour_primaries\fR 4
.IX Item "colour_primaries"
.PD 0
.IP \fBtransfer_characteristics\fR 4
.IX Item "transfer_characteristics"
.IP \fBmatrix_coefficients\fR 4
.IX Item "matrix_coefficients"
.PD
Set the colour description in the stream (see H.264 section E.2.1
and tables E\-3, E\-4 and E\-5).
.IP \fBchroma_sample_loc_type\fR 4
.IX Item "chroma_sample_loc_type"
Set the chroma sample location in the stream (see H.264 section
E.2.1 and figure E\-1).
.IP \fBtick_rate\fR 4
.IX Item "tick_rate"
Set the tick rate (time_scale / num_units_in_tick) in the VUI
parameters.  This is the smallest time unit representable in the
stream, and in many cases represents the field rate of the stream
(double the frame rate).
.IP \fBfixed_frame_rate_flag\fR 4
.IX Item "fixed_frame_rate_flag"
Set whether the stream has fixed framerate \- typically this indicates
that the framerate is exactly half the tick rate, but the exact
meaning is dependent on interlacing and the picture structure (see
H.264 section E.2.1 and table E\-6).
.IP \fBzero_new_constraint_set_flags\fR 4
.IX Item "zero_new_constraint_set_flags"
Zero constraint_set4_flag and constraint_set5_flag in the SPS. These
bits were reserved in a previous version of the H.264 spec, and thus
some hardware decoders require these to be zero. The result of zeroing
this is still a valid bitstream.
.IP \fBcrop_left\fR 4
.IX Item "crop_left"
.PD 0
.IP \fBcrop_right\fR 4
.IX Item "crop_right"
.IP \fBcrop_top\fR 4
.IX Item "crop_top"
.IP \fBcrop_bottom\fR 4
.IX Item "crop_bottom"
.PD
Set the frame cropping offsets in the SPS.  These values will replace
the current ones if the stream is already cropped.
.Sp
These fields are set in pixels.  Note that some sizes may not be
representable if the chroma is subsampled or the stream is interlaced
(see H.264 section *******.1).
.IP \fBsei_user_data\fR 4
.IX Item "sei_user_data"
Insert a string as SEI unregistered user data.  The argument must
be of the form \fIUUID+string\fR, where the UUID is as hex digits
possibly separated by hyphens, and the string can be anything.
.Sp
For example, \fB086f3693\-b7b3\-4f2c\-9653\-21492feee5b8+hello\fR will
insert the string ``hello'' associated with the given UUID.
.IP \fBdelete_filler\fR 4
.IX Item "delete_filler"
Deletes both filler NAL units and filler SEI messages.
.IP \fBdisplay_orientation\fR 4
.IX Item "display_orientation"
Insert, extract or remove Display orientation SEI messages.
See H.264 section D.1.27 and D.2.27 for syntax and semantics.
.RS 4
.IP \fBpass\fR 4
.IX Item "pass"
.PD 0
.IP \fBinsert\fR 4
.IX Item "insert"
.IP \fBremove\fR 4
.IX Item "remove"
.IP \fBextract\fR 4
.IX Item "extract"
.RE
.RS 4
.PD
.Sp
Default is pass.
.Sp
Insert mode works in conjunction with \f(CW\*(C`rotate\*(C'\fR and \f(CW\*(C`flip\*(C'\fR options.
Any pre-existing Display orientation messages will be removed in insert or remove mode.
Extract mode attaches the display matrix to the packet as side data.
.RE
.IP \fBrotate\fR 4
.IX Item "rotate"
Set rotation in display orientation SEI (anticlockwise angle in degrees).
Range is \-360 to +360. Default is NaN.
.IP \fBflip\fR 4
.IX Item "flip"
Set flip in display orientation SEI.
.RS 4
.IP \fBhorizontal\fR 4
.IX Item "horizontal"
.PD 0
.IP \fBvertical\fR 4
.IX Item "vertical"
.RE
.RS 4
.PD
.Sp
Default is unset.
.RE
.IP \fBlevel\fR 4
.IX Item "level"
Set the level in the SPS.  Refer to H.264 section A.3 and tables A\-1
to A\-5.
.Sp
The argument must be the name of a level (for example, \fB4.2\fR), a
level_idc value (for example, \fB42\fR), or the special name \fBauto\fR
indicating that the filter should attempt to guess the level from the
input stream properties.
.SS h264_mp4toannexb
.IX Subsection "h264_mp4toannexb"
Convert an H.264 bitstream from length prefixed mode to start code
prefixed mode (as defined in the Annex B of the ITU-T H.264
specification).
.PP
This is required by some streaming formats, typically the MPEG\-2
transport stream format (muxer \f(CW\*(C`mpegts\*(C'\fR).
.PP
For example to remux an MP4 file containing an H.264 stream to mpegts
format with \fBffmpeg\fR, you can use the command:
.PP
.Vb 1
\&        ffmpeg \-i INPUT.mp4 \-codec copy \-bsf:v h264_mp4toannexb OUTPUT.ts
.Ve
.PP
Please note that this filter is auto-inserted for MPEG-TS (muxer
\&\f(CW\*(C`mpegts\*(C'\fR) and raw H.264 (muxer \f(CW\*(C`h264\*(C'\fR) output formats.
.SS h264_redundant_pps
.IX Subsection "h264_redundant_pps"
This applies a specific fixup to some Blu-ray streams which contain
redundant PPSs modifying irrelevant parameters of the stream which
confuse other transformations which require correct extradata.
.SS hevc_metadata
.IX Subsection "hevc_metadata"
Modify metadata embedded in an HEVC stream.
.IP \fBaud\fR 4
.IX Item "aud"
Insert or remove AUD NAL units in all access units of the stream.
.RS 4
.IP \fBinsert\fR 4
.IX Item "insert"
.PD 0
.IP \fBremove\fR 4
.IX Item "remove"
.RE
.RS 4
.RE
.IP \fBsample_aspect_ratio\fR 4
.IX Item "sample_aspect_ratio"
.PD
Set the sample aspect ratio in the stream in the VUI parameters.
.IP \fBvideo_format\fR 4
.IX Item "video_format"
.PD 0
.IP \fBvideo_full_range_flag\fR 4
.IX Item "video_full_range_flag"
.PD
Set the video format in the stream (see H.265 section E.3.1 and
table E.2).
.IP \fBcolour_primaries\fR 4
.IX Item "colour_primaries"
.PD 0
.IP \fBtransfer_characteristics\fR 4
.IX Item "transfer_characteristics"
.IP \fBmatrix_coefficients\fR 4
.IX Item "matrix_coefficients"
.PD
Set the colour description in the stream (see H.265 section E.3.1
and tables E.3, E.4 and E.5).
.IP \fBchroma_sample_loc_type\fR 4
.IX Item "chroma_sample_loc_type"
Set the chroma sample location in the stream (see H.265 section
E.3.1 and figure E.1).
.IP \fBtick_rate\fR 4
.IX Item "tick_rate"
Set the tick rate in the VPS and VUI parameters (time_scale /
num_units_in_tick). Combined with \fBnum_ticks_poc_diff_one\fR, this can
set a constant framerate in the stream.  Note that it is likely to be
overridden by container parameters when the stream is in a container.
.IP \fBnum_ticks_poc_diff_one\fR 4
.IX Item "num_ticks_poc_diff_one"
Set poc_proportional_to_timing_flag in VPS and VUI and use this value
to set num_ticks_poc_diff_one_minus1 (see H.265 sections ******* and
E.3.1).  Ignored if \fBtick_rate\fR is not also set.
.IP \fBcrop_left\fR 4
.IX Item "crop_left"
.PD 0
.IP \fBcrop_right\fR 4
.IX Item "crop_right"
.IP \fBcrop_top\fR 4
.IX Item "crop_top"
.IP \fBcrop_bottom\fR 4
.IX Item "crop_bottom"
.PD
Set the conformance window cropping offsets in the SPS.  These values
will replace the current ones if the stream is already cropped.
.Sp
These fields are set in pixels.  Note that some sizes may not be
representable if the chroma is subsampled (H.265 section *******.1).
.IP \fBwidth\fR 4
.IX Item "width"
.PD 0
.IP \fBheight\fR 4
.IX Item "height"
.PD
Set width and height after crop.
.IP \fBlevel\fR 4
.IX Item "level"
Set the level in the VPS and SPS.  See H.265 section A.4 and tables
A.6 and A.7.
.Sp
The argument must be the name of a level (for example, \fB5.1\fR), a
\&\fIgeneral_level_idc\fR value (for example, \fB153\fR for level 5.1),
or the special name \fBauto\fR indicating that the filter should
attempt to guess the level from the input stream properties.
.SS hevc_mp4toannexb
.IX Subsection "hevc_mp4toannexb"
Convert an HEVC/H.265 bitstream from length prefixed mode to start code
prefixed mode (as defined in the Annex B of the ITU-T H.265
specification).
.PP
This is required by some streaming formats, typically the MPEG\-2
transport stream format (muxer \f(CW\*(C`mpegts\*(C'\fR).
.PP
For example to remux an MP4 file containing an HEVC stream to mpegts
format with \fBffmpeg\fR, you can use the command:
.PP
.Vb 1
\&        ffmpeg \-i INPUT.mp4 \-codec copy \-bsf:v hevc_mp4toannexb OUTPUT.ts
.Ve
.PP
Please note that this filter is auto-inserted for MPEG-TS (muxer
\&\f(CW\*(C`mpegts\*(C'\fR) and raw HEVC/H.265 (muxer \f(CW\*(C`h265\*(C'\fR or
\&\f(CW\*(C`hevc\*(C'\fR) output formats.
.SS imxdump
.IX Subsection "imxdump"
Modifies the bitstream to fit in MOV and to be usable by the Final Cut
Pro decoder. This filter only applies to the mpeg2video codec, and is
likely not needed for Final Cut Pro 7 and newer with the appropriate
\&\fB\-tag:v\fR.
.PP
For example, to remux 30 MB/sec NTSC IMX to MOV:
.PP
.Vb 1
\&        ffmpeg \-i input.mxf \-c copy \-bsf:v imxdump \-tag:v mx3n output.mov
.Ve
.SS mjpeg2jpeg
.IX Subsection "mjpeg2jpeg"
Convert MJPEG/AVI1 packets to full JPEG/JFIF packets.
.PP
MJPEG is a video codec wherein each video frame is essentially a
JPEG image. The individual frames can be extracted without loss,
e.g. by
.PP
.Vb 1
\&        ffmpeg \-i ../some_mjpeg.avi \-c:v copy frames_%d.jpg
.Ve
.PP
Unfortunately, these chunks are incomplete JPEG images, because
they lack the DHT segment required for decoding. Quoting from
<\fBhttp://www.digitalpreservation.gov/formats/fdd/fdd000063.shtml\fR>:
.PP
Avery Lee, writing in the rec.video.desktop newsgroup in 2001,
commented that "MJPEG, or at least the MJPEG in AVIs having the
MJPG fourcc, is restricted JPEG with a fixed \-\- and *omitted* \-\-
Huffman table. The JPEG must be YCbCr colorspace, it must be 4:2:2,
and it must use basic Huffman encoding, not arithmetic or
progressive. . . . You can indeed extract the MJPEG frames and
decode them with a regular JPEG decoder, but you have to prepend
the DHT segment to them, or else the decoder won't have any idea
how to decompress the data. The exact table necessary is given in
the OpenDML spec."
.PP
This bitstream filter patches the header of frames extracted from an MJPEG
stream (carrying the AVI1 header ID and lacking a DHT segment) to
produce fully qualified JPEG images.
.PP
.Vb 3
\&        ffmpeg \-i mjpeg\-movie.avi \-c:v copy \-bsf:v mjpeg2jpeg frame_%d.jpg
\&        exiftran \-i \-9 frame*.jpg
\&        ffmpeg \-i frame_%d.jpg \-c:v copy rotated.avi
.Ve
.SS mjpegadump
.IX Subsection "mjpegadump"
Add an MJPEG A header to the bitstream, to enable decoding by
Quicktime.
.SS mov2textsub
.IX Subsection "mov2textsub"
Extract a representable text file from MOV subtitles, stripping the
metadata header from each subtitle packet.
.PP
See also the \fBtext2movsub\fR filter.
.SS mpeg2_metadata
.IX Subsection "mpeg2_metadata"
Modify metadata embedded in an MPEG\-2 stream.
.IP \fBdisplay_aspect_ratio\fR 4
.IX Item "display_aspect_ratio"
Set the display aspect ratio in the stream.
.Sp
The following fixed values are supported:
.RS 4
.IP \fB4/3\fR 4
.IX Item "4/3"
.PD 0
.IP \fB16/9\fR 4
.IX Item "16/9"
.IP \fB221/100\fR 4
.IX Item "221/100"
.RE
.RS 4
.PD
.Sp
Any other value will result in square pixels being signalled instead
(see H.262 section 6.3.3 and table 6\-3).
.RE
.IP \fBframe_rate\fR 4
.IX Item "frame_rate"
Set the frame rate in the stream.  This is constructed from a table
of known values combined with a small multiplier and divisor \- if
the supplied value is not exactly representable, the nearest
representable value will be used instead (see H.262 section 6.3.3
and table 6\-4).
.IP \fBvideo_format\fR 4
.IX Item "video_format"
Set the video format in the stream (see H.262 section 6.3.6 and
table 6\-6).
.IP \fBcolour_primaries\fR 4
.IX Item "colour_primaries"
.PD 0
.IP \fBtransfer_characteristics\fR 4
.IX Item "transfer_characteristics"
.IP \fBmatrix_coefficients\fR 4
.IX Item "matrix_coefficients"
.PD
Set the colour description in the stream (see H.262 section 6.3.6
and tables 6\-7, 6\-8 and 6\-9).
.SS mpeg4_unpack_bframes
.IX Subsection "mpeg4_unpack_bframes"
Unpack DivX-style packed B\-frames.
.PP
DivX-style packed B\-frames are not valid MPEG\-4 and were only a
workaround for the broken Video for Windows subsystem.
They use more space, can cause minor AV sync issues, require more
CPU power to decode (unless the player has some decoded picture queue
to compensate the 2,0,2,0 frame per packet style) and cause
trouble if copied into a standard container like mp4 or mpeg\-ps/ts,
because MPEG\-4 decoders may not be able to decode them, since they are
not valid MPEG\-4.
.PP
For example to fix an AVI file containing an MPEG\-4 stream with
DivX-style packed B\-frames using \fBffmpeg\fR, you can use the command:
.PP
.Vb 1
\&        ffmpeg \-i INPUT.avi \-codec copy \-bsf:v mpeg4_unpack_bframes OUTPUT.avi
.Ve
.SS noise
.IX Subsection "noise"
Damages the contents of packets or simply drops them without damaging the
container. Can be used for fuzzing or testing error resilience/concealment.
.PP
Parameters:
.IP \fBamount\fR 4
.IX Item "amount"
Accepts an expression whose evaluation per-packet determines how often bytes in that
packet will be modified. A value below 0 will result in a variable frequency.
Default is 0 which results in no modification. However, if neither amount nor drop is specified,
amount will be set to \fI\-1\fR. See below for accepted variables.
.IP \fBdrop\fR 4
.IX Item "drop"
Accepts an expression evaluated per-packet whose value determines whether that packet is dropped.
Evaluation to a positive value results in the packet being dropped. Evaluation to a negative
value results in a variable chance of it being dropped, roughly inverse in proportion to the magnitude
of the value. Default is 0 which results in no drops. See below for accepted variables.
.IP \fBdropamount\fR 4
.IX Item "dropamount"
Accepts a non-negative integer, which assigns a variable chance of it being dropped, roughly inverse
in proportion to the value. Default is 0 which results in no drops. This option is kept for backwards
compatibility and is equivalent to setting drop to a negative value with the same magnitude
i.e. \f(CW\*(C`dropamount=4\*(C'\fR is the same as \f(CW\*(C`drop=\-4\*(C'\fR. Ignored if drop is also specified.
.PP
Both \f(CW\*(C`amount\*(C'\fR and \f(CW\*(C`drop\*(C'\fR accept expressions containing the following variables:
.IP \fBn\fR 4
.IX Item "n"
The index of the packet, starting from zero.
.IP \fBtb\fR 4
.IX Item "tb"
The timebase for packet timestamps.
.IP \fBpts\fR 4
.IX Item "pts"
Packet presentation timestamp.
.IP \fBdts\fR 4
.IX Item "dts"
Packet decoding timestamp.
.IP \fBnopts\fR 4
.IX Item "nopts"
Constant representing AV_NOPTS_VALUE.
.IP \fBstartpts\fR 4
.IX Item "startpts"
First non\-AV_NOPTS_VALUE PTS seen in the stream.
.IP \fBstartdts\fR 4
.IX Item "startdts"
First non\-AV_NOPTS_VALUE DTS seen in the stream.
.IP \fBduration\fR 4
.IX Item "duration"
.PD 0
.IP \fBd\fR 4
.IX Item "d"
.PD
Packet duration, in timebase units.
.IP \fBpos\fR 4
.IX Item "pos"
Packet position in input; may be \-1 when unknown or not set.
.IP \fBsize\fR 4
.IX Item "size"
Packet size, in bytes.
.IP \fBkey\fR 4
.IX Item "key"
Whether packet is marked as a keyframe.
.IP \fBstate\fR 4
.IX Item "state"
A pseudo random integer, primarily derived from the content of packet payload.
.PP
\fIExamples\fR
.IX Subsection "Examples"
.PP
Apply modification to every byte but don't drop any packets.
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-c copy \-bsf noise=1 output.mkv
.Ve
.PP
Drop every video packet not marked as a keyframe after timestamp 30s but do not
modify any of the remaining packets.
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-c copy \-bsf:v noise=drop=\*(Aqgt(t\e,30)*not(key)\*(Aq output.mkv
.Ve
.PP
Drop one second of audio every 10 seconds and add some random noise to the rest.
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-c copy \-bsf:a noise=amount=\-1:drop=\*(Aqbetween(mod(t\e,10)\e,9\e,10)\*(Aq output.mkv
.Ve
.SS null
.IX Subsection "null"
This bitstream filter passes the packets through unchanged.
.SS pcm_rechunk
.IX Subsection "pcm_rechunk"
Repacketize PCM audio to a fixed number of samples per packet or a fixed packet
rate per second. This is similar to the \fBasetnsamples audio
filter\fR but works on audio packets instead of audio frames.
.IP "\fBnb_out_samples, n\fR" 4
.IX Item "nb_out_samples, n"
Set the number of samples per each output audio packet. The number is intended
as the number of samples \fIper each channel\fR. Default value is 1024.
.IP "\fBpad, p\fR" 4
.IX Item "pad, p"
If set to 1, the filter will pad the last audio packet with silence, so that it
will contain the same number of samples (or roughly the same number of samples,
see \fBframe_rate\fR) as the previous ones. Default value is 1.
.IP "\fBframe_rate, r\fR" 4
.IX Item "frame_rate, r"
This option makes the filter output a fixed number of packets per second instead
of a fixed number of samples per packet. If the audio sample rate is not
divisible by the frame rate then the number of samples will not be constant but
will vary slightly so that each packet will start as close to the frame
boundary as possible. Using this option has precedence over \fBnb_out_samples\fR.
.PP
You can generate the well known 1602\-1601\-1602\-1601\-1602 pattern of 48kHz audio
for NTSC frame rate using the \fBframe_rate\fR option.
.PP
.Vb 1
\&        ffmpeg \-f lavfi \-i sine=r=48000:d=1 \-c pcm_s16le \-bsf pcm_rechunk=r=30000/1001 \-f framecrc \-
.Ve
.SS pgs_frame_merge
.IX Subsection "pgs_frame_merge"
Merge a sequence of PGS Subtitle segments ending with an "end of display set"
segment into a single packet.
.PP
This is required by some containers that support PGS subtitles
(muxer \f(CW\*(C`matroska\*(C'\fR).
.SS prores_metadata
.IX Subsection "prores_metadata"
Modify color property metadata embedded in prores stream.
.IP \fBcolor_primaries\fR 4
.IX Item "color_primaries"
Set the color primaries.
Available values are:
.RS 4
.IP \fBauto\fR 4
.IX Item "auto"
Keep the same color primaries property (default).
.IP \fBunknown\fR 4
.IX Item "unknown"
.PD 0
.IP \fBbt709\fR 4
.IX Item "bt709"
.IP \fBbt470bg\fR 4
.IX Item "bt470bg"
.PD
BT601 625
.IP \fBsmpte170m\fR 4
.IX Item "smpte170m"
BT601 525
.IP \fBbt2020\fR 4
.IX Item "bt2020"
.PD 0
.IP \fBsmpte431\fR 4
.IX Item "smpte431"
.PD
DCI P3
.IP \fBsmpte432\fR 4
.IX Item "smpte432"
P3 D65
.RE
.RS 4
.RE
.IP \fBtransfer_characteristics\fR 4
.IX Item "transfer_characteristics"
Set the color transfer.
Available values are:
.RS 4
.IP \fBauto\fR 4
.IX Item "auto"
Keep the same transfer characteristics property (default).
.IP \fBunknown\fR 4
.IX Item "unknown"
.PD 0
.IP \fBbt709\fR 4
.IX Item "bt709"
.PD
BT 601, BT 709, BT 2020
.IP \fBsmpte2084\fR 4
.IX Item "smpte2084"
SMPTE ST 2084
.IP \fBarib\-std\-b67\fR 4
.IX Item "arib-std-b67"
ARIB STD\-B67
.RE
.RS 4
.RE
.IP \fBmatrix_coefficients\fR 4
.IX Item "matrix_coefficients"
Set the matrix coefficient.
Available values are:
.RS 4
.IP \fBauto\fR 4
.IX Item "auto"
Keep the same colorspace property (default).
.IP \fBunknown\fR 4
.IX Item "unknown"
.PD 0
.IP \fBbt709\fR 4
.IX Item "bt709"
.IP \fBsmpte170m\fR 4
.IX Item "smpte170m"
.PD
BT 601
.IP \fBbt2020nc\fR 4
.IX Item "bt2020nc"
.RE
.RS 4
.RE
.PP
Set Rec709 colorspace for each frame of the file
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-c copy \-bsf:v prores_metadata=color_primaries=bt709:color_trc=bt709:colorspace=bt709 output.mov
.Ve
.PP
Set Hybrid Log-Gamma parameters for each frame of the file
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-c copy \-bsf:v prores_metadata=color_primaries=bt2020:color_trc=arib\-std\-b67:colorspace=bt2020nc output.mov
.Ve
.SS remove_extra
.IX Subsection "remove_extra"
Remove extradata from packets.
.PP
It accepts the following parameter:
.IP \fBfreq\fR 4
.IX Item "freq"
Set which frame types to remove extradata from.
.RS 4
.IP \fBk\fR 4
.IX Item "k"
Remove extradata from non-keyframes only.
.IP \fBkeyframe\fR 4
.IX Item "keyframe"
Remove extradata from keyframes only.
.IP "\fBe, all\fR" 4
.IX Item "e, all"
Remove extradata from all frames.
.RE
.RS 4
.RE
.SS setts
.IX Subsection "setts"
Set PTS and DTS in packets.
.PP
It accepts the following parameters:
.IP \fBts\fR 4
.IX Item "ts"
.PD 0
.IP \fBpts\fR 4
.IX Item "pts"
.IP \fBdts\fR 4
.IX Item "dts"
.PD
Set expressions for PTS, DTS or both.
.IP \fBduration\fR 4
.IX Item "duration"
Set expression for duration.
.IP \fBtime_base\fR 4
.IX Item "time_base"
Set output time base.
.PP
The expressions are evaluated through the eval API and can contain the following
constants:
.IP \fBN\fR 4
.IX Item "N"
The count of the input packet. Starting from 0.
.IP \fBTS\fR 4
.IX Item "TS"
The demux timestamp in input in case of \f(CW\*(C`ts\*(C'\fR or \f(CW\*(C`dts\*(C'\fR option or presentation
timestamp in case of \f(CW\*(C`pts\*(C'\fR option.
.IP \fBPOS\fR 4
.IX Item "POS"
The original position in the file of the packet, or undefined if undefined
for the current packet
.IP \fBDTS\fR 4
.IX Item "DTS"
The demux timestamp in input.
.IP \fBPTS\fR 4
.IX Item "PTS"
The presentation timestamp in input.
.IP \fBDURATION\fR 4
.IX Item "DURATION"
The duration in input.
.IP \fBSTARTDTS\fR 4
.IX Item "STARTDTS"
The DTS of the first packet.
.IP \fBSTARTPTS\fR 4
.IX Item "STARTPTS"
The PTS of the first packet.
.IP \fBPREV_INDTS\fR 4
.IX Item "PREV_INDTS"
The previous input DTS.
.IP \fBPREV_INPTS\fR 4
.IX Item "PREV_INPTS"
The previous input PTS.
.IP \fBPREV_INDURATION\fR 4
.IX Item "PREV_INDURATION"
The previous input duration.
.IP \fBPREV_OUTDTS\fR 4
.IX Item "PREV_OUTDTS"
The previous output DTS.
.IP \fBPREV_OUTPTS\fR 4
.IX Item "PREV_OUTPTS"
The previous output PTS.
.IP \fBPREV_OUTDURATION\fR 4
.IX Item "PREV_OUTDURATION"
The previous output duration.
.IP \fBNEXT_DTS\fR 4
.IX Item "NEXT_DTS"
The next input DTS.
.IP \fBNEXT_PTS\fR 4
.IX Item "NEXT_PTS"
The next input PTS.
.IP \fBNEXT_DURATION\fR 4
.IX Item "NEXT_DURATION"
The next input duration.
.IP \fBTB\fR 4
.IX Item "TB"
The timebase of stream packet belongs.
.IP \fBTB_OUT\fR 4
.IX Item "TB_OUT"
The output timebase.
.IP \fBSR\fR 4
.IX Item "SR"
The sample rate of stream packet belongs.
.IP \fBNOPTS\fR 4
.IX Item "NOPTS"
The AV_NOPTS_VALUE constant.
.PP
For example, to set PTS equal to DTS (not recommended if B\-frames are involved):
.PP
.Vb 1
\&        ffmpeg \-i INPUT \-c:a copy \-bsf:a setts=pts=DTS out.mkv
.Ve
.SS showinfo
.IX Subsection "showinfo"
Log basic packet information. Mainly useful for testing, debugging,
and development.
.SS text2movsub
.IX Subsection "text2movsub"
Convert text subtitles to MOV subtitles (as used by the \f(CW\*(C`mov_text\*(C'\fR
codec) with metadata headers.
.PP
See also the \fBmov2textsub\fR filter.
.SS trace_headers
.IX Subsection "trace_headers"
Log trace output containing all syntax elements in the coded stream
headers (everything above the level of individual coded blocks).
This can be useful for debugging low-level stream issues.
.PP
Supports AV1, H.264, H.265, (M)JPEG, MPEG\-2 and VP9, but depending
on the build only a subset of these may be available.
.SS truehd_core
.IX Subsection "truehd_core"
Extract the core from a TrueHD stream, dropping ATMOS data.
.SS vp9_metadata
.IX Subsection "vp9_metadata"
Modify metadata embedded in a VP9 stream.
.IP \fBcolor_space\fR 4
.IX Item "color_space"
Set the color space value in the frame header.  Note that any frame
set to RGB will be implicitly set to PC range and that RGB is
incompatible with profiles 0 and 2.
.RS 4
.IP \fBunknown\fR 4
.IX Item "unknown"
.PD 0
.IP \fBbt601\fR 4
.IX Item "bt601"
.IP \fBbt709\fR 4
.IX Item "bt709"
.IP \fBsmpte170\fR 4
.IX Item "smpte170"
.IP \fBsmpte240\fR 4
.IX Item "smpte240"
.IP \fBbt2020\fR 4
.IX Item "bt2020"
.IP \fBrgb\fR 4
.IX Item "rgb"
.RE
.RS 4
.RE
.IP \fBcolor_range\fR 4
.IX Item "color_range"
.PD
Set the color range value in the frame header.  Note that any value
imposed by the color space will take precedence over this value.
.RS 4
.IP \fBtv\fR 4
.IX Item "tv"
.PD 0
.IP \fBpc\fR 4
.IX Item "pc"
.RE
.RS 4
.RE
.PD
.SS vp9_superframe
.IX Subsection "vp9_superframe"
Merge VP9 invisible (alt-ref) frames back into VP9 superframes. This
fixes merging of split/segmented VP9 streams where the alt-ref frame
was split from its visible counterpart.
.SS vp9_superframe_split
.IX Subsection "vp9_superframe_split"
Split VP9 superframes into single frames.
.SS vp9_raw_reorder
.IX Subsection "vp9_raw_reorder"
Given a VP9 stream with correct timestamps but possibly out of order,
insert additional show-existing-frame packets to correct the ordering.
.SH "SEE ALSO"
.IX Header "SEE ALSO"
\&\fBffmpeg\fR\|(1), \fBffplay\fR\|(1), \fBffprobe\fR\|(1), \fBlibavcodec\fR\|(3)
.SH AUTHORS
.IX Header "AUTHORS"
The FFmpeg developers.
.PP
For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
\&\fBgit log\fR in the FFmpeg source directory, or browsing the
online repository at <\fBhttps://git.ffmpeg.org/ffmpeg\fR>.
.PP
Maintainers for the specific components are listed in the file
\&\fIMAINTAINERS\fR in the source code tree.
