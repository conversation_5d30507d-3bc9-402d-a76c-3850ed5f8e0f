#include "codec_internal.h"
#include "decode.h"
#include "profiles.h"
#include <stdlib.h>

#include "eac3_dolby.h"
#include "profiles.h"

#define NUM_FRAMES_MAX_BY_PKT 100 // to be adjusted if needed

/* NUM_OUTPUT_SAMPLES indicates the maximum number of samples we want to get from the decoder at once, */
/* its value has to be less than requirements.max_out_samples. */
/* If there are less samples available, the number of obtained samples will be smaller */
#define NUM_OUTPUT_SAMPLES (1536)


static
int
dlb_warnings_find(const dlb_warnings *warnings, int warning_id)
{
    for (unsigned int idx = 0; idx < warnings->num_warnings; ++idx)
    {
        if (warnings->warnings[idx] == warning_id)
        {
            return 1;
        }
    }

    return 0;
}


static void set_ch_layout_map(const AVCodecContext* avctx, const int joc_mode, AVChannelCustom* map)
{
    if(!map)
    {
        av_log(avctx, AV_LOG_ERROR, "map is null");
        return;
    }

    switch(joc_mode)
    {
        case DLB_PADC_SPEAKER_CONFIG_STEREO:
            map[0].id = AV_CHAN_FRONT_LEFT;
            map[1].id = AV_CHAN_FRONT_RIGHT;
            break;
            
        case DLB_PADC_SPEAKER_CONFIG_5_1:
            map[0].id  = AV_CHAN_FRONT_LEFT;
            map[1].id  = AV_CHAN_FRONT_RIGHT;
            map[2].id  = AV_CHAN_FRONT_CENTER;
            map[3].id  = AV_CHAN_LOW_FREQUENCY;
            map[4].id  = AV_CHAN_SIDE_LEFT;
            map[5].id  = AV_CHAN_SIDE_RIGHT;
            break;

        case DLB_PADC_SPEAKER_CONFIG_7_1:
            map[0].id = AV_CHAN_FRONT_LEFT;
            map[1].id = AV_CHAN_FRONT_RIGHT;
            map[2].id = AV_CHAN_FRONT_CENTER;
            map[3].id = AV_CHAN_LOW_FREQUENCY;
            map[4].id = AV_CHAN_SIDE_LEFT;
            map[5].id = AV_CHAN_SIDE_RIGHT;
            map[6].id = AV_CHAN_BACK_LEFT;
            map[7].id = AV_CHAN_BACK_RIGHT;
            break;

        case DLB_PADC_SPEAKER_CONFIG_5_1_2:
            map[0].id = AV_CHAN_FRONT_LEFT;
            map[1].id = AV_CHAN_FRONT_RIGHT;
            map[2].id = AV_CHAN_FRONT_CENTER;
            map[3].id = AV_CHAN_LOW_FREQUENCY;
            map[4].id = AV_CHAN_SIDE_LEFT;
            map[5].id = AV_CHAN_SIDE_RIGHT;
            map[6].id = AV_CHAN_TOP_SIDE_LEFT;
            map[7].id = AV_CHAN_TOP_SIDE_RIGHT;
            break;

        case DLB_PADC_SPEAKER_CONFIG_5_1_4:
            map[0].id = AV_CHAN_FRONT_LEFT;
            map[1].id = AV_CHAN_FRONT_RIGHT;
            map[2].id = AV_CHAN_FRONT_CENTER;
            map[3].id = AV_CHAN_LOW_FREQUENCY;
            map[4].id = AV_CHAN_SIDE_LEFT;
            map[5].id = AV_CHAN_SIDE_RIGHT;
            map[6].id = AV_CHAN_TOP_FRONT_LEFT;
            map[7].id = AV_CHAN_TOP_FRONT_RIGHT;
            map[8].id = AV_CHAN_TOP_BACK_LEFT;
            map[9].id = AV_CHAN_TOP_BACK_RIGHT;
            break;

        case DLB_PADC_SPEAKER_CONFIG_7_1_4:
            map[0].id  = AV_CHAN_FRONT_LEFT;
            map[1].id  = AV_CHAN_FRONT_RIGHT;
            map[2].id  = AV_CHAN_FRONT_CENTER;
            map[3].id  = AV_CHAN_LOW_FREQUENCY;
            map[4].id  = AV_CHAN_SIDE_LEFT;
            map[5].id  = AV_CHAN_SIDE_RIGHT;
            map[6].id  = AV_CHAN_BACK_LEFT;
            map[7].id  = AV_CHAN_BACK_RIGHT;
            map[8].id  = AV_CHAN_TOP_FRONT_LEFT;
            map[9].id  = AV_CHAN_TOP_FRONT_RIGHT;
            map[10].id = AV_CHAN_TOP_BACK_LEFT;
            map[11].id = AV_CHAN_TOP_BACK_RIGHT;
            break;

        case DLB_PADC_SPEAKER_CONFIG_9_1_6:
            map[0].id  = AV_CHAN_FRONT_LEFT;
            map[1].id  = AV_CHAN_FRONT_RIGHT;
            map[2].id  = AV_CHAN_FRONT_CENTER;
            map[3].id  = AV_CHAN_LOW_FREQUENCY;
            map[4].id  = AV_CHAN_SIDE_LEFT;
            map[5].id  = AV_CHAN_SIDE_RIGHT;
            map[6].id  = AV_CHAN_BACK_LEFT;
            map[7].id  = AV_CHAN_BACK_RIGHT;
            map[8].id  = AV_CHAN_WIDE_LEFT;
            map[9].id  = AV_CHAN_WIDE_RIGHT;
            map[10].id = AV_CHAN_TOP_FRONT_LEFT;
            map[11].id = AV_CHAN_TOP_FRONT_RIGHT;
            map[12].id = AV_CHAN_TOP_SIDE_LEFT;
            map[13].id = AV_CHAN_TOP_SIDE_RIGHT;
            map[14].id = AV_CHAN_TOP_BACK_LEFT;
            map[15].id = AV_CHAN_TOP_BACK_RIGHT;
            break;
        
        default:
            av_log(avctx, AV_LOG_ERROR, "Wrong joc value, map not set");
            return;
    }
}


static
void
metadata_updated_callback(const dlb_padc_ui_md *p_md, void *p_data)
{
    AVCodecContext* avctx = (AVCodecContext*)p_data;
    DolbyAC3DecodeContext* s = avctx->priv_data;

    if(p_md->type == DLB_PADC_UI_MD_TYPE_DDP)
    {
        pthread_mutex_lock(&s->mutex_metadata);

        // Bitstream properties parameters
        s->ac3_metadata.atmos_presence    = p_md->codec_md.ddp.stream_properties.atmos_presence;
        s->ac3_metadata.channel_mode      = p_md->codec_md.ddp.stream_properties.channel_mode;
        s->ac3_metadata.crc_error_present = p_md->codec_md.ddp.stream_properties.crc_error_present;
        s->ac3_metadata.data_rate         = p_md->codec_md.ddp.stream_properties.data_rate;
        s->ac3_metadata.sample_rate       = p_md->codec_md.ddp.stream_properties.sample_rate;

        // Intelligent loudness parameters
        s->ac3_metadata.loud_corr_type     = p_md->codec_md.ddp.intloud.loud_corr_type;
        s->ac3_metadata.loud_reg_type      = p_md->codec_md.ddp.intloud.loud_reg_type;
        s->ac3_metadata.dialogue_corrected = p_md->codec_md.ddp.intloud.dialogue_corrected;

        // BSI parameters
        s->ac3_metadata.dialnorm      = p_md->codec_md.ddp.bsi_md.dialnorm;
        s->ac3_metadata.dmixmod       = p_md->codec_md.ddp.bsi_md.dmixmod;
        s->ac3_metadata.lfeon         = p_md->codec_md.ddp.bsi_md.lfeon;
        s->ac3_metadata.lorocmixlev   = p_md->codec_md.ddp.bsi_md.lorocmixlev;
        s->ac3_metadata.lorosurmixlev = p_md->codec_md.ddp.bsi_md.lorosurmixlev;
        s->ac3_metadata.ltrtcmixlev   = p_md->codec_md.ddp.bsi_md.ltrtcmixlev;
        s->ac3_metadata.ltrtsurmixlev = p_md->codec_md.ddp.bsi_md.ltrtsurmixlev;

        if(s->ac3_metadata.atmos_presence)
        {
            s->atmos_metadata.ac3_metadata = s->ac3_metadata;

            // Dolby Atmos parameters
            s->atmos_metadata.bed_object_configuration[0]  = p_md->codec_md.ddp.atmos_md.bed_object_configuration.L;
            s->atmos_metadata.bed_object_configuration[1]  = p_md->codec_md.ddp.atmos_md.bed_object_configuration.R;
            s->atmos_metadata.bed_object_configuration[2]  = p_md->codec_md.ddp.atmos_md.bed_object_configuration.C;
            s->atmos_metadata.bed_object_configuration[3]  = p_md->codec_md.ddp.atmos_md.bed_object_configuration.LFE;
            s->atmos_metadata.bed_object_configuration[4]  = p_md->codec_md.ddp.atmos_md.bed_object_configuration.LS;
            s->atmos_metadata.bed_object_configuration[5]  = p_md->codec_md.ddp.atmos_md.bed_object_configuration.RS;
            s->atmos_metadata.bed_object_configuration[6]  = p_md->codec_md.ddp.atmos_md.bed_object_configuration.LRS;
            s->atmos_metadata.bed_object_configuration[7]  = p_md->codec_md.ddp.atmos_md.bed_object_configuration.RRS;
            s->atmos_metadata.bed_object_configuration[8]  = p_md->codec_md.ddp.atmos_md.bed_object_configuration.LFH;
            s->atmos_metadata.bed_object_configuration[9]  = p_md->codec_md.ddp.atmos_md.bed_object_configuration.RFH;
            s->atmos_metadata.bed_object_configuration[10] = p_md->codec_md.ddp.atmos_md.bed_object_configuration.LTM;
            s->atmos_metadata.bed_object_configuration[11] = p_md->codec_md.ddp.atmos_md.bed_object_configuration.RTM;
            s->atmos_metadata.bed_object_configuration[12] = p_md->codec_md.ddp.atmos_md.bed_object_configuration.LRH;
            s->atmos_metadata.bed_object_configuration[13] = p_md->codec_md.ddp.atmos_md.bed_object_configuration.RRH;
            s->atmos_metadata.bed_object_configuration[14] = p_md->codec_md.ddp.atmos_md.bed_object_configuration.LW;
            s->atmos_metadata.bed_object_configuration[15] = p_md->codec_md.ddp.atmos_md.bed_object_configuration.RW;
            s->atmos_metadata.bed_object_configuration[16] = p_md->codec_md.ddp.atmos_md.bed_object_configuration.LFE2;

            s->atmos_metadata.complexity_index    = p_md->codec_md.ddp.atmos_md.complexity_index;
            s->atmos_metadata.dynamic_objects_cnt = p_md->codec_md.ddp.atmos_md.dynamic_objects_cnt;
            s->atmos_metadata.height_trim_5_1     = (int32_t)p_md->codec_md.ddp.atmos_md.height_trim_5_1;
            s->atmos_metadata.surround_trim_5_1   = (int32_t)p_md->codec_md.ddp.atmos_md.surround_trim_5_1;

            // Dynamic object parameters
            for(int i = 0; i<s->atmos_metadata.dynamic_objects_cnt; i++)
            {
                s->atmos_metadata.dialogue_indication[i] = (int32_t)p_md->codec_md.ddp.atmos_md.objects_info[i].dialogue_indication;
                s->atmos_metadata.x[i] = (float)p_md->codec_md.ddp.atmos_md.objects_info[i].x;
                s->atmos_metadata.y[i] = (float)p_md->codec_md.ddp.atmos_md.objects_info[i].y;
                s->atmos_metadata.z[i] = (float)p_md->codec_md.ddp.atmos_md.objects_info[i].z;
            }
        }


        s->metadata_filled = 1;
        pthread_mutex_unlock(&s->mutex_metadata);
    }
    else
    {
        av_log(avctx, AV_LOG_ERROR, "Unsupported type");
    }
}


static av_cold int common_dolby_end(AVCodecContext *avctx)
{
    DolbyAC3DecodeContext *s = avctx->priv_data;
    
    int status = 0;

    for(int i = 0; i<NUM_FRAMES_MAX_BY_PKT; i++)
    {
        status = dlb_pamif_md_segment_destroy(&s->output_pcm[i].p_md);
        if (status != DLB_PAMIF_STATUS_OK)
        {
            av_log(avctx, AV_LOG_ERROR, "App failed to deallocate metadata. Error code: %d\n", status);
        }
        free(s->output_pcm[i].p_pcm);
    }
    free(s->output_pcm);

    status = dlb_padc_close(s->h_state);
    if (status != DLB_PADC_STATUS_OK)
    {
        av_log(avctx, AV_LOG_ERROR, "App failed to close PADC. Error code: %d\n", status);
        return AVERROR_EXTERNAL;
    }

    av_channel_layout_uninit(&avctx->ch_layout);

    return 0;
}


static av_cold int common_dolby_init(AVCodecContext *avctx, DolbyAC3DecodeContext *s)
{
    s->h_state = NULL;

    dlb_padc_init_config  init_config;
    dlb_padc_requirements requirements;

    /* Set up a valid initialization-time configuration structure */
    /* dlb_padc_setup sets default init values into init_config */
    int status = dlb_padc_setup(DLB_PADC_TYPE_DDP, &init_config);
    if (status != DLB_PADC_STATUS_OK)
    {
        av_log(avctx, AV_LOG_ERROR, "App failed to setup PADC. Error code %d", status);
        return AVERROR_EXTERNAL;
    }


    /* Configure initialization-time configuration structure */
    /* 
     * This is the place for setting default configuration values.
     * Some of them can be changed later by setting run-time parameters.
     */

    /* Select DRC mode to be applied while decoding the bitstream */
    init_config.config.ddp.drc_setting = s->compression_mode;

    /* Select speaker_config */
    if(s->atmos_decoding_enabled)
    {
        init_config.config.ddp.speaker_config = s->joc_mode;
    }
    else if(s->downmix_mode!=-1)
    {
        init_config.config.ddp.speaker_config = DLB_PADC_SPEAKER_CONFIG_STEREO;
        init_config.config.ddp.dmx_mode = s->downmix_mode;
    }
    else
    {
        init_config.config.ddp.speaker_config = DLB_PADC_SPEAKER_CONFIG_7_1; // if dmx and atmos are disabled, we set speaker_config to the max possible for dd/ddp, 
                                                                             // and the channels in excess will be cropped after, according to the original 
                                                                             // speaker_config extracted from metadata
    }

    /* Assign the callback function used to handle metadata update */
    s->metadata_filled = 0; // just to know if the metadata struct has been filled at least once
    init_config.p_callback = metadata_updated_callback;
    /* Assign user-specific data passed into callback */
    init_config.p_user_data = avctx;

    /* Query for the requirements for a particular initialization configuration */
    status = dlb_padc_query(&init_config, &requirements);
    if (status != DLB_PADC_STATUS_OK)
    {
        av_log(avctx, AV_LOG_ERROR, "App failed to query requirements. Error code %d\n", status);
        return AVERROR_EXTERNAL;
    }


    /* Allocate memory for the PCM buffer */
    s->output_pcm = (dlb_paio_pcm_block*)malloc( NUM_FRAMES_MAX_BY_PKT * sizeof(dlb_paio_pcm_block) );
    if(s->output_pcm == NULL)
    {
        av_log(avctx, AV_LOG_ERROR, "Memory allocation failed for output_pcm\n");
        return AVERROR_EXTERNAL;
    }

    for(int i = 0; i<NUM_FRAMES_MAX_BY_PKT; i++)
    {
        s->output_pcm[i].p_pcm = (int32_t*)malloc(requirements.max_out_samples * requirements.max_out_channels * sizeof(int32_t));
        if(s->output_pcm[i].p_pcm == NULL)
        {
            av_log(avctx, AV_LOG_ERROR, "Memory allocation failed for output_pcm[%d].p_pcm\n", i);

            // free previous allocations
            for(int j = 0; j<i; j++)
            {
                free(s->output_pcm[j].p_pcm);
            }
            free(s->output_pcm);

            return AVERROR_EXTERNAL;
        }

        /* Allocate memory for the metadata structure */
        status = dlb_pamif_md_segment_create(&s->output_pcm[i].p_md);
        if(status != DLB_PAMIF_STATUS_OK)
        {
            av_log(avctx, AV_LOG_ERROR, "App failed to allocate metadata for output_pcm[%d]. Error code: %d\n", i, status);

            // free previous allocations
            for(int j = 0; j<i; j++)
            {
                status = dlb_pamif_md_segment_destroy(&s->output_pcm[j].p_md);
                if (status != DLB_PAMIF_STATUS_OK)
                {
                    av_log(avctx, AV_LOG_ERROR, "App failed to deallocate metadata. Error code: %d\n", status);
                }
                free(s->output_pcm[j].p_pcm);
            }
            free(s->output_pcm);

            return AVERROR_EXTERNAL;
        }
    }

    /* Open the decoder instance */
    status = dlb_padc_open(&s->h_state, &init_config);
    if(status != DLB_PADC_STATUS_OK)
    {
        av_log(avctx, AV_LOG_ERROR, "App failed to open PADC. Error code: %d\n", status);

        // free previous allocations
        for(int i = 0; i<NUM_FRAMES_MAX_BY_PKT; i++)
        {
            status = dlb_pamif_md_segment_destroy(&s->output_pcm[i].p_md);
            if (status != DLB_PAMIF_STATUS_OK)
            {
                av_log(avctx, AV_LOG_ERROR, "App failed to deallocate metadata. Error code: %d\n", status);
            }
            free(s->output_pcm[i].p_pcm);
        }
        free(s->output_pcm);

        return AVERROR_EXTERNAL;
    }

    if (NUM_OUTPUT_SAMPLES >= requirements.max_out_samples)
    {
        av_log(avctx, AV_LOG_ERROR, "NUM_OUTPUT_SAMPLES value too big\n");
        common_dolby_end(avctx);
        return AVERROR_EXTERNAL;
    }


    /* Initializing input_bitstream structure. */
    /* Dolby Digital Plus consists of a single bitstream, therefore num_bitstreams is 1 */
    /* and further references to p_bitstreams and num_bytes use 0 as an index */
    s->input_bitstream.num_bitstreams = 1;
    s->input_bitstream.p_bitstreams[0] = NULL;
    s->input_bitstream.num_bytes[0] = 0;

    return 0;
}


static av_cold int ac3_dolby_init(AVCodecContext *avctx)
{
    DolbyAC3DecodeContext *s = avctx->priv_data;
    s->atmos_decoding_enabled = 0;
    return common_dolby_init(avctx, s);
}


static av_cold int atmos_dolby_init(AVCodecContext *avctx)
{
    DolbyAC3DecodeContext *s = avctx->priv_data;
    s->atmos_decoding_enabled = 1;
    return common_dolby_init(avctx, s);
}


/**
 * Decode a single AC-3 frame.
 */
static int common_dolby_frame(AVCodecContext *avctx, AVFrame *frame, int *got_frame_ptr, AVPacket *avpkt)
{
    DolbyAC3DecodeContext *s = avctx->priv_data;

    *got_frame_ptr = 0;

    int status = 0;
    int is_process_ready = 0;
    uint32_t input_buffer_space_left = 0;

    s->input_bitstream.p_bitstreams[0] = avpkt->data;
    s->input_bitstream.num_bytes[0] = avpkt->size;

    /* dlb_padc_add_bytes consumes all of the input data or none of it */
    /* If there is not enough space at the input, the user needs to call */
    /* dlb_padc_process before trying to add more data. */
    status = dlb_padc_add_bytes(s->h_state, &s->input_bitstream, &is_process_ready, &input_buffer_space_left);
    if (status != DLB_PADC_STATUS_OK)
    {
        if (status == DLB_PADC_STATUS_WARNING)
        {
            dlb_warnings warnings = dlb_padc_warnings_get(s->h_state);

            if (dlb_warnings_find(&warnings, DLB_PADC_WARN_INPUT_FULL))
            {
                av_log(avctx, AV_LOG_ERROR, "App failed to consume bitstream data: The decoder input is full\n");
            }
        }
        else
        {
            av_log(avctx, AV_LOG_ERROR, "App failed to consume bitstream data. Error code %d\n", status);
        }
        return AVERROR_EXTERNAL;
    }

    uint32_t output_samples_available = 0;
    unsigned int out_frame_cnt = 0;
    unsigned int total_num_samples = 0;
    while (is_process_ready)
    {
        /* Process the data, is_process_ready indicates whether further */ 
        /* processing can be done using the data previously added. */
        status = dlb_padc_process(s->h_state, &is_process_ready, &output_samples_available);

        if(status != DLB_PADC_STATUS_OK)
        {
            if (status == DLB_PADC_STATUS_WARNING)
            {
                const dlb_warnings warnings = dlb_padc_warnings_get(s->h_state);

                for (unsigned int idx = 0; idx < warnings.num_warnings; ++idx)
                {
                    switch (warnings.warnings[idx])
                    {
                        case DLB_PADC_WARN_INPUT_FULL:
                            av_log(avctx, AV_LOG_ERROR, "App failed to process the data: Input Full\n");
                            break;
                        case DLB_PADC_WARN_OUTPUT_FULL:
                            av_log(avctx, AV_LOG_ERROR, "App failed to process the data: Output Full\n");
                            break;
                        case DLB_PADC_WARN_UNABLE_TO_DECODE:
                            av_log(avctx, AV_LOG_ERROR, "App failed to process the data: Unable to Decode\n");
                            break;
                        case DLB_PADC_WARN_PROGRAM_OUT_OF_RANGE:
                            av_log(avctx, AV_LOG_ERROR, "App failed to process the data: Program out of range\n");
                            break;
                        case DLB_PADC_WARN_FRAME_INVALID:
                            av_log(avctx, AV_LOG_ERROR, "App failed to process the data: Frame Invalid\n");
                            break;
                        case DLB_PADC_WARN_UNSUPPORTED_SUBSTREAM:
                            av_log(avctx, AV_LOG_ERROR, "App failed to process the data: Unsupported substream\n");
                            break;
                    }
                }
            }
            else
            {
                av_log(avctx, AV_LOG_ERROR, "App failed to process the data. Error code %d\n", status);
                return AVERROR_EXTERNAL;
            }            
        }

        while (output_samples_available > NUM_OUTPUT_SAMPLES)
        {
            /* Obtain samples from the decoder */
            status = dlb_padc_get_samples(s->h_state, NUM_OUTPUT_SAMPLES, &s->output_pcm[out_frame_cnt], &output_samples_available);

            if (status != DLB_PADC_STATUS_OK)
            {
                av_log(avctx, AV_LOG_ERROR, "App failed to retrieve decoded data. Error code: %d\n", status);
                return AVERROR_EXTERNAL;
            }
            else
            {
                total_num_samples += s->output_pcm[out_frame_cnt].num_samples;
                out_frame_cnt++;
            }
        }
    }


    /* determine profile */
    pthread_mutex_lock(&s->mutex_metadata);
    if(s->metadata_filled)
    {
        if(s->ac3_metadata.atmos_presence)
        {
            avctx->profile = AV_PROFILE_EAC3_DDP_ATMOS;
        }
        else
        {
            avctx->profile = AV_PROFILE_UNKNOWN;
        }
    }
    pthread_mutex_unlock(&s->mutex_metadata);


    /* Decoder call sequence done, we can fill the avframe */
    if(total_num_samples > 0)
    {
        /* determine output channels number */
        pthread_mutex_lock(&s->mutex_metadata);
        if(!s->metadata_filled)
        {
            av_log(avctx, AV_LOG_WARNING, "Metadata info not extracted yet, missing info for the output frame\n");
            return avpkt->size;
        }

        /* set channel number of output according to the config and info from metadata */
        int nb_channels = 0;

        if(s->ac3_metadata.atmos_presence && s->atmos_decoding_enabled)
        {
            nb_channels = atmos_nb_channels_tab[ s->joc_mode ];
        }
        else if(s->downmix_mode!=-1)
        {
            nb_channels = 2;
        }
        else
        {
            nb_channels = ac3_nb_channels_tab[ s->ac3_metadata.channel_mode ] + s->ac3_metadata.lfeon;
        }
        pthread_mutex_unlock(&s->mutex_metadata);
        

        avctx->sample_fmt  = AV_SAMPLE_FMT_S32;
        avctx->sample_rate = 48000;

        frame->nb_samples = total_num_samples;
        frame->format     = AV_SAMPLE_FMT_S32;

        /* set ch_layout for avctx and frame */
        if(s->ac3_metadata.atmos_presence && s->atmos_decoding_enabled)
        {
            av_channel_layout_custom_init(&avctx->ch_layout, nb_channels);
            av_channel_layout_custom_init(&frame->ch_layout, nb_channels);

            set_ch_layout_map(avctx, s->joc_mode, avctx->ch_layout.u.map);
            set_ch_layout_map(avctx, s->joc_mode, frame->ch_layout.u.map);
        }
        else
        {
            av_channel_layout_default(&avctx->ch_layout, nb_channels);
            av_channel_layout_default(&frame->ch_layout, nb_channels);
        }

        /* allocate avframe's buffers */
        if( (status = ff_get_buffer(avctx, frame, 0)) < 0 )
        {
            av_log(avctx, AV_LOG_ERROR, "Failed to allocate avframe's buffers, ret = %d\n", status);
            return AVERROR_EXTERNAL;
        }

        /* copy decoded data into output avframe */
        unsigned int num_samples_written = 0;
        for(int f = 0; f<out_frame_cnt; f++)
        {
            // output_pcm has always 16 channels and is interlaced 
            // so if nb_channels<16, for each sample we need to get the 
            // sample value for the channels we want
            if(nb_channels == 16) // simple : 1 big memcpy does the job
            {
                memcpy(frame->data[0] + num_samples_written * nb_channels * sizeof(uint32_t), 
                    s->output_pcm[f].p_pcm, 
                    s->output_pcm[f].num_samples * nb_channels * sizeof(uint32_t));
            }
            else // 1 memcpy by sample
            {
                for(int samp = 0; samp<s->output_pcm[f].num_samples; samp++)
                {
                    memcpy(frame->data[0] + (num_samples_written+samp) * nb_channels * sizeof(uint32_t), 
                            &s->output_pcm[f].p_pcm[samp*16], 
                            nb_channels * sizeof(uint32_t));
                }
            }

            num_samples_written += s->output_pcm[f].num_samples;

            // reset num_samples for next calls
            s->output_pcm[f].num_samples = 0;
        }

        /* add new side_data to the frame */
        pthread_mutex_lock(&s->mutex_metadata);
        if(s->ac3_metadata.atmos_presence && s->atmos_decoding_enabled)
        {
            AVFrameSideData* side_data = av_frame_new_side_data(frame, AV_FRAME_DATA_ATMOS_METADATA, sizeof(DolbyAtmosMetadata));
            DolbyAtmosMetadata* const atmos_metadata = (DolbyAtmosMetadata* const)(side_data->data);
            *atmos_metadata = s->atmos_metadata;
        }
        else
        {
            AVFrameSideData* side_data = av_frame_new_side_data(frame, AV_FRAME_DATA_AC3_METADATA, sizeof(DolbyAC3Metadata));
            DolbyAC3Metadata* const ac3_metadata = (DolbyAC3Metadata* const)(side_data->data);
            *ac3_metadata = s->ac3_metadata;
        }
        pthread_mutex_unlock(&s->mutex_metadata);

        *got_frame_ptr = 1;
    }

    return avpkt->size;
}


#define OFFSET(x) offsetof(DolbyAC3DecodeContext, x)
#define PAR (AV_OPT_FLAG_DECODING_PARAM | AV_OPT_FLAG_AUDIO_PARAM)

static const AVOption options_ac3[] = {
    {"downmix_mode", "enable stereo downmix mode", OFFSET(downmix_mode), AV_OPT_TYPE_INT, {.i64 = -1}, -1, 3, PAR, "downmix_mode"},
        {"none", "No downmix",                        0, AV_OPT_TYPE_CONST, {.i64 = -1}                   , -1, 3, PAR, "downmix_mode"},
        {"auto", "Auto",                              0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_DOWNMIX_AUTO}, -1, 3, PAR, "downmix_mode"},
        {"LtRt", "Dolby Surround compatible (Lt/Rt)", 0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_DOWNMIX_LTRT}, -1, 3, PAR, "downmix_mode"},
        {"LoRo", "Stereo (Lo/Ro)",                    0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_DOWNMIX_LORO}, -1, 3, PAR, "downmix_mode"},

    {"compression_mode", "chose compression mode", OFFSET(compression_mode), AV_OPT_TYPE_INT, {.i64 = DLB_PADC_DRC_NONE}, 0, 2, PAR, "compression_mode"},
        {"none", "No compression", 0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_DRC_NONE}, 0, 2, PAR, "compression_mode"},
        {"line", "Line",           0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_DRC_LINE}, 0, 2, PAR, "compression_mode"},
        {"rf",   "RF",             0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_DRC_RF}  , 0, 2, PAR, "compression_mode"},

    {NULL},
};

static const AVOption options_atmos[] = {
    {"downmix_mode", "enable stereo downmix mode", OFFSET(downmix_mode), AV_OPT_TYPE_INT, {.i64 = -1}, -1, 3, PAR, "downmix_mode"},
        {"none", "No downmix",                        0, AV_OPT_TYPE_CONST, {.i64 = -1}                   , -1, 3, PAR, "downmix_mode"},
        {"auto", "Auto",                              0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_DOWNMIX_AUTO}, -1, 3, PAR, "downmix_mode"},
        {"LtRt", "Dolby Surround compatible (Lt/Rt)", 0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_DOWNMIX_LTRT}, -1, 3, PAR, "downmix_mode"},
        {"LoRo", "Stereo (Lo/Ro)",                    0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_DOWNMIX_LORO}, -1, 3, PAR, "downmix_mode"},

    {"compression_mode", "chose compression mode", OFFSET(compression_mode), AV_OPT_TYPE_INT, {.i64 = DLB_PADC_DRC_NONE}, 0, 2, PAR, "compression_mode"},
        {"none", "No compression", 0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_DRC_NONE}, 0, 2, PAR, "compression_mode"},
        {"line", "Line",           0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_DRC_LINE}, 0, 2, PAR, "compression_mode"},
        {"rf",   "RF",             0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_DRC_RF}  , 0, 2, PAR, "compression_mode"},

    {"joc_mode", "enable joc decoding mode", OFFSET(joc_mode), AV_OPT_TYPE_INT, {.i64 = DLB_PADC_SPEAKER_CONFIG_9_1_6}, 0, 6, PAR, "joc_mode"},
        {"2.0",   "2.0",   0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_SPEAKER_CONFIG_STEREO}, 0, 6, PAR, "joc_mode"},
        {"5.1",   "5.1",   0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_SPEAKER_CONFIG_5_1  } , 0, 6, PAR, "joc_mode"},
        {"7.1",   "7.1",   0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_SPEAKER_CONFIG_7_1  } , 0, 6, PAR, "joc_mode"},
        {"5.1.2", "5.1.2", 0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_SPEAKER_CONFIG_5_1_2} , 0, 6, PAR, "joc_mode"},
        {"5.1.4", "5.1.4", 0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_SPEAKER_CONFIG_5_1_4} , 0, 6, PAR, "joc_mode"},
        {"7.1.4", "7.1.4", 0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_SPEAKER_CONFIG_7_1_4} , 0, 6, PAR, "joc_mode"},
        {"9.1.6", "9.1.6", 0, AV_OPT_TYPE_CONST, {.i64 = DLB_PADC_SPEAKER_CONFIG_9_1_6} , 0, 6, PAR, "joc_mode"},

    {NULL},
};


/*****************/
/****** AC3 ******/
/*****************/
static const AVClass ac3_dolby_class = {
    .class_name = "AC-3 official Dolby decoder",
    .item_name = av_default_item_name,
    .option = options_ac3,
    .version = LIBAVUTIL_VERSION_INT,
};

const FFCodec ff_ac3_dolby_decoder = {
    .p.name = "ac3_dolby",
    .p.long_name = NULL_IF_CONFIG_SMALL("ATSC A/52A (AC-3)"),
    .p.type = AVMEDIA_TYPE_AUDIO,
    .p.id = AV_CODEC_ID_AC3,
    .priv_data_size = sizeof(DolbyAC3DecodeContext),
    .init = ac3_dolby_init,
    .close = common_dolby_end,
    FF_CODEC_DECODE_CB(common_dolby_frame),
    .p.capabilities = AV_CODEC_CAP_DR1 | AV_CODEC_CAP_CHANNEL_CONF,
    .p.sample_fmts = (const enum AVSampleFormat[]) {AV_SAMPLE_FMT_S32,
                                                  AV_SAMPLE_FMT_NONE},
    .p.priv_class = &ac3_dolby_class,
};


/*****************/
/****** EAC3 *****/
/*****************/
static const AVClass eac3_dolby_class = {
    .class_name = "EAC-3 official Dolby decoder",
    .item_name = av_default_item_name,
    .option = options_ac3,
    .version = LIBAVUTIL_VERSION_INT,
};

const FFCodec ff_eac3_dolby_decoder = {
    .p.name = "eac3_dolby",
    .p.long_name = NULL_IF_CONFIG_SMALL("ATSC A/52B (E-AC-3)"),
    .p.type = AVMEDIA_TYPE_AUDIO,
    .p.id = AV_CODEC_ID_EAC3,
    .priv_data_size = sizeof(DolbyAC3DecodeContext),
    .init = ac3_dolby_init,
    .close = common_dolby_end,
    FF_CODEC_DECODE_CB(common_dolby_frame),
    .p.capabilities = AV_CODEC_CAP_DR1 | AV_CODEC_CAP_CHANNEL_CONF,
    .p.sample_fmts = (const enum AVSampleFormat[]) {AV_SAMPLE_FMT_S32,
                                                  AV_SAMPLE_FMT_NONE},
    .p.priv_class = &eac3_dolby_class,
    .p.profiles = NULL_IF_CONFIG_SMALL(ff_eac3_profiles),
};


/*****************/
/***** ATMOS *****/
/*****************/
static const AVClass atmos_dolby_class = {
    .class_name = "Atmos official Dolby decoder",
    .item_name = av_default_item_name,
    .option = options_atmos,
    .version = LIBAVUTIL_VERSION_INT,
};

const FFCodec ff_atmos_dolby_decoder = {
    .p.name = "atmos_dolby",
    .p.long_name = NULL_IF_CONFIG_SMALL("Dolby ATMOS"),
    .p.type = AVMEDIA_TYPE_AUDIO,
    .p.id = AV_CODEC_ID_EAC3,
    .priv_data_size = sizeof(DolbyAC3DecodeContext),
    .init = atmos_dolby_init,
    .close = common_dolby_end,
    FF_CODEC_DECODE_CB(common_dolby_frame),
    .p.capabilities = AV_CODEC_CAP_DR1 | AV_CODEC_CAP_CHANNEL_CONF,
    .p.sample_fmts = (const enum AVSampleFormat[]) {AV_SAMPLE_FMT_S32,
                                                    AV_SAMPLE_FMT_NONE},
    .p.priv_class = &atmos_dolby_class,
    .p.profiles   = NULL_IF_CONFIG_SMALL(ff_eac3_profiles),
};